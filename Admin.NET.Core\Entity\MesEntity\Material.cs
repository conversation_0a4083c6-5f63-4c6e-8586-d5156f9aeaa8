﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Core.Entity.MesEntity;
/// <summary>
/// 物料实体表
/// </summary>
[SugarTable(null, "物料实体类表")]
[SysTable]
public class MaterialEntity: EntityBaseDel
{
    /// <summary>
    /// 物料编号
    /// </summary>
    public string MaterialCode { get; set; }

    /// <summary>
    /// 是否系统编号（开关状态）
    /// </summary>
    public bool IsSystemCode { get; set; }

    /// <summary>
    /// 物料名称（必填）
    /// </summary>
    public string MaterialName { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string Specification { get; set; }

    /// <summary>
    /// 单位（如个、件等，需提前维护可选值）
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 物料类型（必填，下拉选项值）
    /// </summary>
    public string MaterialType { get; set; }

    /// <summary>
    /// 物料属性（下拉选项值）
    /// </summary>
    public string MaterialAttribute { get; set; }

    /// <summary>
    /// 物料分类（下拉选项值）
    /// </summary>
    public string MaterialCategory { get; set; }

    /// <summary>
    /// 状态（启用/禁用，默认启用）
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 有效期（天数）
    /// </summary>
    public int ValidityDays { get; set; }

    /// <summary>
    /// 报警天数（提前天数）
    /// </summary>
    public int? AlarmDays { get; set; }

    /// <summary>
    /// 库存上限
    /// </summary>
    public decimal? StockUpperLimit { get; set; }

    /// <summary>
    /// 库存下限
    /// </summary>
    public decimal? StockLowerLimit { get; set; }

    /// <summary>
    /// 采购价格
    /// </summary>
    public decimal? PurchasePrice { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    public string Remarks { get; set; }

    /// <summary>
    /// 物料图片（若需存储路径，可定义为 string；若需二进制，根据实际调整）
    /// 这里简单用 string 存路径示例
    /// </summary>
    public string ImagePath { get; set; }
    /// <summary>
    /// BOM表单Id
    /// </summary>
    public long BomID { get; set;}
}
