﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.Tasks.Dto;
/// <summary>
/// 添加报表记录
/// </summary>
public class ShowReportRecordDto
{
    /// <summary>
    /// 任务Id
    /// </summary>
    public long TaskId { get; set; }
    /// <summary>
    /// 任务编号
    /// </summary>
    public string TaskNumber { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    public string TaskName { get; set; }
    /// <summary>
    /// 工单Id
    /// </summary>
    public long WorkkOrderId { get; set; }
    /// <summary>
    /// 工单编号
    /// </summary>
    public string WorkOrderCode { get; set; }
    /// <summary>
    /// 工单名称
    /// </summary>
    public string WorkOrderName { get; set; }
    /// <summary>
    /// 站点Id
    /// </summary>
    public long SitesId { get; set; }
    /// <summary>
    /// 站点编号
    /// </summary>
    public string SiteCode { get; set; }
    /// <summary>
    /// 站点名称
    /// </summary>
    public string SiteName { get; set; }
    /// <summary>
    /// 工艺路线Id
    /// </summary>
    public long ProcessRouteId { get; set; }
    /// <summary>
    /// 工艺路线编号
    /// </summary>
    public string ProcessRouteCode { get; set; }

    /// <summary>
    /// 工艺路线名称234
    /// </summary>
    public string ProcessRouteName { get; set; }
    /// <summary>
    /// 工序步骤Id
    /// </summary>
    public long ProcessstepId { get; set; }
    /// <summary>
    /// 工序名称
    /// </summary>
    public string ProcessName { get; set; }  
    /// <summary>
    /// 工序编号
    /// </summary>
    public string ProcessCode { get; set; }   
    /// <summary>
    /// 班组id
    /// </summary>
    public long ClassgroupId { get; set; }
   /// <summary>
   /// 班组编号
   /// </summary>
    public string ClassGroupCode { get; set; }
    /// <summary>
    /// 班组名称
    /// </summary>
    public string? ClassGroupName { get; set; }
    /// <summary>
    /// 报工人员
    /// </summary>
    public string Reporter { get; set; }

    /// <summary>
    /// 报工数量
    /// </summary>
    public int ReportQuantity { get; set; }

    /// <summary>
    /// 报工时间
    /// </summary>
    public DateTime ReportTime { get; set; }


    /// <summary>
    /// 合格数量
    /// </summary>
    public int Qualified { get; set; }

    /// <summary>
    /// 不合格数量
    /// </summary>
    public int Unqualified { get; set; }

    /// <summary>
    /// 合格率
    /// </summary>
    public decimal QualifiedRate { get; set; }

    /// <summary>
    /// 检测结果
    /// </summary>
    public string DetectionResult { get; set; }
    /// <summary>
    /// 报工记录状态 0:未质检 1:已质检
    /// </summary>
    public int ReportRecordState { get; set; }
}
