// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.Tasks.Dto;
using Admin.NET.Core.Entity.MesEntity;
using AutoMapper;
using Microsoft.AspNetCore.Routing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.Tasks;
[DynamicApiController]
public class TaskService : IDynamicApiController
{
    private readonly SqlSugarRepository<TaskEntity> _taskEntityRep;
    private readonly SqlSugarRepository<Product> _productRep;
    private readonly SqlSugarRepository<ProcessRoute> _processRouteRep;
    private readonly SqlSugarRepository<ReportRecords> _reportRecordsRep;
    private readonly SqlSugarRepository<Admin.NET.Core.Entity.MesEntity.WorkOrder> _WorkOrderRep;
    private readonly IMapper mapper;
    private readonly SqlSugarRepository<Sites> siteRep;
    private readonly SqlSugarRepository<ClassGroup> classgroupRep;

    public TaskService(SqlSugarRepository<TaskEntity> taskEntityRep, SqlSugarRepository<Product> productRep, SqlSugarRepository<ProcessRoute> processRouteRep, SqlSugarRepository<ReportRecords> reportRecordsRep, SqlSugarRepository<Core.Entity.MesEntity.WorkOrder> workOrderRep, IMapper mapper)
    {
        _taskEntityRep = taskEntityRep;
        _productRep = productRep;
        _processRouteRep = processRouteRep;
        _reportRecordsRep = reportRecordsRep;
        _WorkOrderRep = workOrderRep;
        this.mapper = mapper;
        this.siteRep = siteRep;
        this.classgroupRep = classgroupRep;
    }

    /// <summary>
    /// 添加任务
    /// </summary>
    /// <param name="taskdto"></param>
    /// <returns></returns>
    public async Task<int> AddTask(CreateUpdateTaskDto taskdto)
    {
        try
        {
            var res = mapper.Map<TaskEntity>(taskdto);
            await _taskEntityRep.AsInsertable(res).ExecuteReturnIdentityAsync();
            return 1;
        }
        catch (Exception)
        {
            throw;
        }
    }

    /// <summary>
    /// 显示站点列表
    /// </summary>
    /// <returns></returns>
    public async Task<List<Sites>> GetSites()
    {
        try
        {
            var res = await siteRep.GetListAsync();
            return res;
        }
        catch (Exception)
        {

            throw;
        }
    }

    /// <summary>
    /// 修改任务
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPut]
    public async Task<int> UpdTask([FromQuery] long taskId, CreateUpdateTaskDto dto)
    {
        try
        {
            var res = await _taskEntityRep.GetByIdAsync(taskId);
            var entity = mapper.Map(dto, res);
            entity.Id = taskId;
            await _taskEntityRep.AsUpdateable(entity).ExecuteCommandAsync();
            return 1;
        }
        catch (Exception)
        {

            throw;
        }
    }
    /// <summary>
    /// 删除任务
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    [HttpDelete]
    public async Task<int> DelTask([FromQuery] long taskId)
    {
        try
        {
            await _taskEntityRep.AsDeleteable().Where(x => x.Id == taskId).ExecuteCommandAsync();
            return 1;
        }
        catch (Exception)
        {

            throw;
        }
    }

    /// <summary>
    /// 根据工艺路线获取站点工艺信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<List<ShowTaskSitesProcessRouteDto>> GetTaskSitesProcessRouteAsync([FromQuery] QueryTaskSitesProcessRouteDto input)
    {
        var query = _taskEntityRep.AsQueryable()
            .LeftJoin<Sites>((task, site) => task.SitesId == site.Id)
            .LeftJoin<ProcessRoute>((task, site, route) => task.ProcessRouteId == route.Id)
            .LeftJoin<ProcessStep>((task, site, route, step) => task.ProcessStepId == step.ProcessStepId)
            .WhereIF(input.ProcessRouteId.HasValue, (task, site, route, step) => task.ProcessRouteId == input.ProcessRouteId)
            .WhereIF(input.ProcessStepId.HasValue, (task, site, route, step) => step.ProcessStepId == input.ProcessStepId)
            .Select((task, site, route, step) => new ShowTaskSitesProcessRouteDto
            {
                Id = task.Id,
                TaskNumber = task.TaskNumber,
                TaskName = task.TaskName,
                SitesId = task.SitesId,
                SiteCode = site.SiteCode,
                SiteName = site.SiteName,
                ProcessStepId = step.ProcessStepId,
                ProcessName = step.ProcessName,
                ProcessCode = step.ProcessCode,
                ProcessRouteId = route.Id,
                ProcessRouteCode = route.ProcessRouteCode,
                ProcessRouteName = route.ProcessRouteName,
                TaskColor = task.TaskColor,
                PlanQuantity = task.PlanQuantity,
                StartTime = task.StartTime,
                EndTime = task.EndTime,

            });
        return await query.ToListAsync();
    }

    /// <summary>
    /// 获取工单任务
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<GetWorkOrderTaskDto>> GetWorkOrderTask([FromQuery] SearchWorkOrderTaskDto dto)
    {
        try
        {
            var queary = _taskEntityRep.AsQueryable()
                .LeftJoin<Sites>((task, site) => task.SitesId == site.Id)
                .LeftJoin<ProcessRoute>((task, site, route) => task.ProcessRouteId == route.Id)
                .LeftJoin<ProcessStep>((task, site, route, step) => task.ProcessStepId == step.ProcessStepId)
                .LeftJoin<WorkOrder>((task, site, route, step, workorder) => task.WorkOrderId == workorder.Id)
                .WhereIF(!string.IsNullOrEmpty(dto.TaskCode), (task, site, route, step, workorder) => task.TaskNumber.Contains(dto.TaskCode))
                .WhereIF(!string.IsNullOrEmpty(dto.WorkOrderCode), (task, site, route, step, workorder) => workorder.WorkOrderCode.Contains(dto.WorkOrderCode))
                .WhereIF(dto.ProcessStepId.HasValue, (task, site, route, step, workorder) => task.ProcessStepId == dto.ProcessStepId)
                .WhereIF(dto.Satus > 0, (task, site, route, step, workorder) => task.TaskStatus == dto.Satus)
                .Select((task, site, route, step, workorder) => new GetWorkOrderTaskDto
                {
                    TaskNumber = task.TaskNumber,
                    TaskName = task.TaskName,
                    SiteName = site.SiteName,
                    WorkOrderCode = workorder.WorkOrderCode,
                    WorkOrderName = workorder.WorkOrderName,
                    ProcessName = step.ProcessName,
                    ProcessRouteName = route.ProcessRouteName,
                    TaskColor = task.TaskColor,
                    PlanQuantity = task.PlanQuantity,
                    RealityQuantity = 0,
                    StartTime = task.StartTime,
                    RealityEndTime = task.EndTime.AddMonths(6),
                    EndTime = task.EndTime,
                    RealityStartTime = task.StartTime.AddMonths(1),
                    TaskStatus = task.TaskStatus,
                    ProcessCode = step.ProcessCode,
                    TaskId = task.Id,
                });
            var pagelist = await queary.ToPagedListAsync(dto.Index, dto.Size);
            return pagelist;
        }
        catch (Exception)
        {

            throw;
        }
    }

    /// <summary>
    /// 显示班组列表
    /// </summary>
    /// <returns></returns>
    public async Task<List<ClassGroup>> GetClassGroup()
    {
        return await classgroupRep.GetListAsync();
    }

    /// <summary>
    /// 派工
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPut]
    public async Task<TaskEntity> DispatchWork([FromQuery]long taskId,[FromQuery]DispatchWorkDto dto)
    {
        try
        {
            var task = await _taskEntityRep.GetByIdAsync(taskId);
            var list = mapper.Map(dto,task);
            list.Id = taskId;
            list.TaskStatus = 1;
            await _taskEntityRep.UpdateAsync(list);
            return list;
        }
        catch (Exception)
        {

            throw;
        }
    }

    /// <summary>
    /// 修改开工状态为报工
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    public async Task<int> UpdWorkStart(long taskId)
    { 
        try
        { 
            var task = await _taskEntityRep.GetByIdAsync(taskId);
            if(task.TaskStatus == 1)
            {
                task.TaskStatus = 2;
            }
            await _taskEntityRep.UpdateAsync(task);
            return 1;
        }
        catch (Exception)
        {

            throw;
        }
    }
}
