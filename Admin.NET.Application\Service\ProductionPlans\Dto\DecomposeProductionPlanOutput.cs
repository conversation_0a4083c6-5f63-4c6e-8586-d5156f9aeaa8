// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.ProductionPlans.Dto;

/// <summary>
/// 生产计划分解结果输出
/// </summary>
public class DecomposeProductionPlanOutput
{
    /// <summary>
    /// 操作是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 信息提示
    /// </summary>
    public string Message { get; set; }
    
    /// <summary>
    /// 分解的物料列表
    /// </summary>
    public List<DecomposedMaterial> DecomposedMaterials { get; set; } = new List<DecomposedMaterial>();
}

/// <summary>
/// 分解后的物料信息
/// </summary>
public class DecomposedMaterial
{
    /// <summary>
    /// 物料ID
    /// </summary>
    public long MaterialId { get; set; }
    
    /// <summary>
    /// 物料编号
    /// </summary>
    public string MaterialCode { get; set; }
    
    /// <summary>
    /// 物料名称
    /// </summary>
    public string MaterialName { get; set; }
    
    /// <summary>
    /// 规格型号
    /// </summary>
    public string Specification { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }
    
    /// <summary>
    /// 需求数量
    /// </summary>
    public int RequiredQuantity { get; set; }
} 