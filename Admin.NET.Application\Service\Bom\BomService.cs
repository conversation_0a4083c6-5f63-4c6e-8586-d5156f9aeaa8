﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.Bom.Dto;
using Admin.NET.Application.Service.Bom.Dto.PageResult;
using Admin.NET.Core;
using Admin.NET.Core.Entity.MesEntity;
using AngleSharp.Dom;
using AutoMapper;
using Furion.DatabaseAccessor;
using Furion.DynamicApiController;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using Microsoft.AspNetCore.Authorization;

namespace Admin.NET.Application.Service.Bom;



[AllowAnonymous]
[DynamicApiController]
public class BomService : IDynamicApiController
{
    private readonly SqlSugarRepository<BomEntity> _bomRep;
    private readonly SqlSugarRepository<Product> _proRep;
    private readonly SqlSugarRepository<MaterialEntity> _materRep;
    private readonly SqlSugarRepository<BOMMaterials> _bOMMaterialsrRep;
    private readonly SqlSugarRepository<BOMProcess> _bOMProcessRep;
    private readonly SqlSugarRepository<ProcessMaterial> _processMaterialRep;
    private readonly SqlSugarRepository<ProcessRoute> _processRouteRep;
    private readonly SqlSugarRepository<ProcessComposition> _processCompositionRep;
    private readonly IMapper _mapper;

    public BomService(SqlSugarRepository<BomEntity> bomRep, IMapper mapper, SqlSugarRepository<Product> proRep, SqlSugarRepository<MaterialEntity> materRep, SqlSugarRepository<BOMMaterials> bOMMaterialsrRep, SqlSugarRepository<BOMProcess> bOMProcessRep, SqlSugarRepository<ProcessMaterial> processMaterialRep, SqlSugarRepository<ProcessRoute> processRouteRep, SqlSugarRepository<ProcessComposition> processCompositionRep)
    {
        _bomRep = bomRep;
        _mapper = mapper;
        _proRep = proRep;
        _materRep = materRep;
        _bOMMaterialsrRep = bOMMaterialsrRep;
        _bOMProcessRep = bOMProcessRep;
        _processMaterialRep = processMaterialRep;
        _processRouteRep = processRouteRep;
        _processCompositionRep = processCompositionRep;
    }

    /// <summary>
    /// 添加BOM表单
    /// </summary>
    /// <param name="dto">BOM添加DTO</param>
    /// <returns>添加后的BOM实体</returns>
    [HttpPost]
    public async Task<BomEntity> AddBomAsync(BomDto dto)
    {
        using (var tran = new TransactionScope(
            TransactionScopeOption.Required,
            new TransactionOptions 
            { 
                IsolationLevel = IsolationLevel.ReadCommitted,
                Timeout = TimeSpan.FromMinutes(5)
            },
            TransactionScopeAsyncFlowOption.Enabled))
        {
            try
            {
                var entity = _mapper.Map<BomEntity>(dto);
                var result = await _bomRep.AsInsertable(entity).ExecuteReturnEntityAsync();
                
                // 添加BOM物料或者产品表
                foreach (var item in dto.MaterialIDs)
                {
                    var material = new BOMMaterials
                    {
                        BomID = result.Id,
                        MaterialID = item,
                        State = dto.State
                    };
                    await _bOMMaterialsrRep.AsInsertable(material).ExecuteCommandAsync();
                }
                
                // 工序物料
                foreach (var item in dto.MaterialIdsw)
                {
                    var material = new ProcessMaterial
                    {
                        BomID = result.Id,
                        MaterialId = item,
                        State = dto.State,
                        ProcessRouteId = dto.ProcessRouteId,
                        ProcessStepId = dto.ProcessStepId
                    };
                    await _processMaterialRep.AsInsertable(material).ExecuteCommandAsync();
                }

                tran.Complete();
                return result;
            }
            catch
            {
                throw;
            }
        }
    }
    /// <summary>
    /// 产品的显示分页显示
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<Product>> PageProductAsync([FromQuery] SimplePageInput input)
    {

        var query = _proRep.AsQueryable();
        query = query.OrderBy(x => x.Id); // 支持排序
        var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
        return pagedList;
    }
    /// <summary>
    /// 工艺显示分页显示
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<ProcessRoute>> PageProcessRouteAsync([FromQuery] SimplePageInput input)
    {

        var query = _processRouteRep.AsQueryable();
        query = query.OrderBy(x => x.Id); // 支持排序
        var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
        return pagedList;
    }
    /// <summary>
    /// 工序组成分页显示
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<ProcessCompositiondto>> PageProcessCompositionAsync(int Page,int PageSize,string id)
    {

        var query12 = _processCompositionRep.AsQueryable().Where(x=>x.ProcessRouteId==id);
        var query = _processCompositionRep.AsQueryable().Where(x => x.ProcessRouteId == id)
            .LeftJoin<ProcessStep>((a, b) => a.ProcessStepId == b.ProcessStepId).Select((a, b) => new ProcessCompositiondto
            {
                GongxuId = b.ProcessStepId,
                ProcessStepId = a.ProcessStepId,
                ProcessName = b.ProcessName,
                NextProcedure = a.NextProcedure,
                SerialCode = a.SerialCode
            });
        var pagedList = await query.ToPagedListAsync(Page,PageSize);
        return pagedList;
    }

    /// <summary>
    /// BOM的显示分页显示
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<BomEntity>> PageBomEntityAsync([FromQuery] SimplePageInput input)
    {

        var query = _bomRep.AsQueryable();
        query = query.OrderBy(x => x.Id); // 支持排序
        var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
        return pagedList;
    }

    /// <summary>
    /// 物料的分页显示
    /// </summary>
    /// <param name="input">分页参数</param>
    /// <returns>分页后的物料实体列表</returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<MaterialEntity>> PageMaterialAsync([FromQuery] SimplePageInput input)
    {
        var query = _materRep.AsQueryable();
        query = query.OrderBy(x => x.Id); // 支持排序
        var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
        return pagedList;
    }
    /// <summary>
    /// 添加BOM物料或者产品表
    /// </summary>
    /// <param name="dto">BOM添加DTO</param>
    /// <returns>添加后的记录数</returns>
    //[UnitOfWork]
    //[HttpPost]
    //public async Task<int> AddBOMMaterialsAsync(BOMMaterialsAddDTO dto)
    //{
    //    var result = 0;
    //    foreach (var item in dto.MaterialIDs)
    //    {
    //        var material = new BOMMaterials
    //        {
    //            BomCode = dto.BomCode,
    //            MaterialID = item,
    //            State = dto.State
    //        };
    //        // Fix: Use ExecuteCommandAsync to return the number of affected rows instead of trying to add the entity directly to the result.
    //        result += await _bOMMaterialsrRep.AsInsertable(material).ExecuteCommandAsync();
    //    }
    //    return result;
    //}


    /// <summary>
    /// OM物料或者产品表分页显示
    /// </summary>
    /// <param name="input">分页参数</param>
    /// <returns>分页后的物料实体列表</returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<BOMMaterialsGetlist>> PageBOMMaterialsGetlistAsync([FromQuery] SeachBOMMaterialsGetlist input)
    {
        if (input.State == 1)
        {
            // Ensure that the query uses the correct syntax for SqlSugar's join functionality

            var query = _bOMMaterialsrRep.AsQueryable()
                .LeftJoin<MaterialEntity>((a, b) => a.MaterialID == b.Id)
                .Where((a, b) => a.State == input.State && a.BomID == input.BomCode)
                .Select((a, b) => new BOMMaterialsGetlist
                {
                    State = a.State,
                    MaterialCode = b.MaterialCode ,
                    IsSystemCode =  b.IsSystemCode,
                    MaterialName =  b.MaterialName ,
                    Specification =  b.Specification ,
                    Unit = b.Unit ,
                    MaterialType =  b.MaterialType ,
                    MaterialAttribute =  b.MaterialAttribute ,    
                    MaterialCategory = b.MaterialCategory 
                });

           // query = query.OrderBy(x => x.); // 支持排序
            var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
            return pagedList;
        }
        else
        {
            var query = _bOMMaterialsrRep.AsQueryable()
               .LeftJoin<Product>((a, b) => a.MaterialID == b.Id)
               .Where((a, b) => a.State == input.State && a.BomID == input.BomCode)
               .Select((a, b) => new BOMMaterialsGetlist
               {
                   BomCode = a.BomID,
                   State = a.State,
                   IsEnabled = b.Status,
                   Unit = b.Unit,
                   MaterialCode = b.ProductCode,
                   IsSystemCode = b.IsSystemCode,
                   MaterialName = b.ProductName,
                   Specification = b.Specification,
                   MaterialType = b.ProductType,
                   MaterialAttribute = b.ProductAttribute,
                   MaterialCategory = b.ProductCategory
               });

            // query = query.OrderBy(x => x.); // 支持排序
            var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
            return pagedList;
        }

    }
    /// <summary>
    /// 添加BOM工艺表
    /// </summary>
    /// <param name="dto">BOM添加DTO</param>
    /// <returns>添加后的BOM实体</returns>
    [UnitOfWork]
    [HttpPost]
    public async Task<BOMProcess> AddBOMProcessAsync(BOMProcessAddDTO dto)
    {

        var entity = _mapper.Map<BOMProcess>(dto);
        var result = await _bOMProcessRep.AsInsertable(entity).ExecuteReturnEntityAsync();
        return result;
    }
    /// <summary>
    /// 添加工序物料表
    /// </summary>
    /// <param name="dto">BOM添加DTO</param>
    /// <returns>添加后的BOM实体</returns>
    //[UnitOfWork]
    //[HttpPost]

    //public async Task<int> AddProcessMaterialAsync(ProcessMaterialAddDTO dto)
    //{
    //    var result = 0;
    //    foreach (var item in dto.MaterialIds)
    //    {
    //        var material = new ProcessMaterial
    //        {
    //            SerialCode = dto.SerialCode,
    //            MaterialId = item,
    //            State = dto.State,
    //            ProcessRouteId = dto.ProcessRouteId,
    //            ProcessStepId = dto.ProcessStepId
    //        };
    //        // Fix: Use ExecuteCommandAsync to return the number of affected rows instead of trying to add the entity directly to the result.
    //        result += await _processMaterialRep.AsInsertable(material).ExecuteCommandAsync();
    //    }
    //    return result;
    //}

    /// <summary>
    /// 工序物料表分页显示
    /// </summary>
    /// <param name="input">分页参数</param>
    /// <returns>分页后的物料实体列表</returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<BOMProcessMaterialsGetlist>> PageBOMProcessMaterialsGetlistAsync([FromQuery] SeachBOMMaterialsGetlist input)
    {
        if (input.State == 1)
        {
            // Ensure that the query uses the correct syntax for SqlSugar's join functionality

            var query = _processMaterialRep.AsQueryable()
                .LeftJoin<MaterialEntity>((a, b) => a.MaterialId == b.Id)
                .Where((a, b) => a.State == input.State && a.BomID == input.BomCode)
                .Select((a, b) => new BOMProcessMaterialsGetlist
                {
                    State = a.State,
                    MaterialCode = b.MaterialCode,
                    IsSystemCode = b.IsSystemCode,
                    MaterialName = b.MaterialName,
                    Specification = b.Specification,
                    Unit = b.Unit,
                    MaterialType = b.MaterialType,
                    MaterialAttribute = b.MaterialAttribute,
                    MaterialCategory = b.MaterialCategory
                });

            // query = query.OrderBy(x => x.); // 支持排序
            var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
            return pagedList;
        }
        else
        {
            var query = _processMaterialRep.AsQueryable()
                .LeftJoin<Product>((a, b) => a.MaterialId == b.Id)
                .Where((a, b) => a.State == input.State && a.BomID == input.BomCode)
                .Select((a, b) => new BOMProcessMaterialsGetlist
               {
                    BomID = a.BomID,
                   State = a.State,
                   IsEnabled = b.Status,
                   Unit = b.Unit,
                   MaterialCode = b.ProductCode,
                   IsSystemCode = b.IsSystemCode,
                   MaterialName = b.ProductName,
                   Specification = b.Specification,
                   MaterialType = b.ProductType,
                   MaterialAttribute = b.ProductAttribute,
                   MaterialCategory = b.ProductCategory
               });

            // query = query.OrderBy(x => x.); // 支持排序
            var pagedList = await query.ToPagedListAsync(input.Page, input.PageSize);
            return pagedList;
        }

    }
}


