﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using Admin.NET.Core.Service;
using Furion.DynamicApiController;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.ProductionPlans;

/// <summary>
/// 生产计划附件上传服务
/// </summary>
[ApiDescriptionSettings(Order = 100)]
[DynamicApiController]
public class ProductionPlanUploadService : IDynamicApiController
{
    private readonly SysFileService _fileService;

    public ProductionPlanUploadService(SysFileService fileService)
    {
        _fileService = fileService;
    }
    
    /// <summary>
    /// 上传生产计划附件
    /// </summary>
    /// <param name="file">文件</param>
    /// <returns>文件信息</returns>
    [HttpPost]
    public async Task<SysFile> UploadAttachment( IFormFile file)
    {
        var uploadFileInput = new UploadFileInput
        {
            File = file,
            FileType = "productionplan",
            IsPublic = false,
            AllowSuffix = ".docx,.xls,.xlsx,.zip,.rar,.jpg,.jpeg,.png"
        };
        var sysFile = await _fileService.UploadFile(uploadFileInput, "productionplan");
        return sysFile;
    }
}
