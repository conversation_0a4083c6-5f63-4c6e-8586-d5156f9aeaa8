// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.ProductionPlans;
using Admin.NET.Application.Service.ProductionPlans.Dto;
using Admin.NET.Core.Entity.MesEntity;
using Admin.NET.Core.Enum;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace Admin.NET.Test.ProductionPlan;

/// <summary>
/// 生产计划分解功能测试
/// </summary>
public class ProductionPlanDecomposeTest : BaseTest
{
    private readonly ProductionPlanService _productionPlanService;

    public ProductionPlanDecomposeTest()
    {
        _productionPlanService = _serviceProvider.GetRequiredService<ProductionPlanService>();
    }

    /// <summary>
    /// 测试分解不存在的计划（应该失败）
    /// </summary>
    [Fact]
    public async Task DecomposeNonExistentPlan_ShouldFail()
    {
        // Arrange
        var input = new DecomposeProductionPlanInput
        {
            PlanId = 99999 // 不存在的计划ID
        };

        // Act
        var result = await _productionPlanService.DecomposeProductionPlanAsync(input);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("未找到指定的生产计划", result.Message);
    }

    /// <summary>
    /// 测试分解已分解的计划（应该失败）
    /// </summary>
    [Fact]
    public async Task DecomposeAlreadyDecomposedPlan_ShouldFail()
    {
        // Arrange
        var input = new DecomposeProductionPlanInput
        {
            PlanId = 1 // 假设存在一个已分解的计划
        };

        // Act
        var result = await _productionPlanService.DecomposeProductionPlanAsync(input);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("只有未分解的计划才能进行分解操作", result.Message);
    }

    /// <summary>
    /// 测试分解没有关联BOM的计划（应该失败）
    /// </summary>
    [Fact]
    public async Task DecomposePlanWithoutBom_ShouldFail()
    {
        // Arrange
        var input = new DecomposeProductionPlanInput
        {
            PlanId = 2 // 假设存在一个未关联BOM的计划
        };

        // Act
        var result = await _productionPlanService.DecomposeProductionPlanAsync(input);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("生产计划未关联BOM", result.Message);
    }

    /// <summary>
    /// 测试分解BOM中没有物料的计划（应该失败）
    /// </summary>
    [Fact]
    public async Task DecomposePlanWithEmptyBom_ShouldFail()
    {
        // Arrange
        var input = new DecomposeProductionPlanInput
        {
            PlanId = 3 // 假设存在一个BOM中没有物料的计划
        };

        // Act
        var result = await _productionPlanService.DecomposeProductionPlanAsync(input);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("BOM中没有关联的物料信息", result.Message);
    }

    /// <summary>
    /// 测试成功分解包含物料和半成品的计划
    /// </summary>
    [Fact]
    public async Task DecomposeValidPlanWithMaterialsAndProducts_ShouldSucceed()
    {
        // 这个测试需要先创建完整的测试数据：
        // 1. 生产计划
        // 2. 主BOM
        // 3. 子BOM（半成品）
        // 4. 物料
        // 5. 产品
        // 6. BOM物料关系
        
        // 由于涉及复杂的数据库操作，这里只是示例结构
        // 在实际测试中，需要使用测试数据库或内存数据库
        
        // Arrange
        var input = new DecomposeProductionPlanInput
        {
            PlanId = 4 // 假设存在一个有效的计划
        };

        // Act
        var result = await _productionPlanService.DecomposeProductionPlanAsync(input);

        // Assert
        Assert.True(result.Success);
        Assert.Contains("生产计划分解成功", result.Message);
        Assert.NotEmpty(result.DecomposedMaterials);
        
        // 验证生成的工单类型
        // 应该包含：物料工单(MAT)、半成品工单(SUB)、主产品工单(MAIN)
    }

    /// <summary>
    /// 测试分解只包含物料的计划
    /// </summary>
    [Fact]
    public async Task DecomposePlanWithOnlyMaterials_ShouldSucceed()
    {
        // Arrange
        var input = new DecomposeProductionPlanInput
        {
            PlanId = 5 // 假设存在一个只有物料的计划
        };

        // Act
        var result = await _productionPlanService.DecomposeProductionPlanAsync(input);

        // Assert
        Assert.True(result.Success);
        Assert.Contains("生产计划分解成功", result.Message);
        Assert.NotEmpty(result.DecomposedMaterials);
        
        // 验证只生成了物料工单和主产品工单
    }

    /// <summary>
    /// 测试分解包含多个半成品的计划
    /// </summary>
    [Fact]
    public async Task DecomposePlanWithMultipleSubProducts_ShouldSucceed()
    {
        // Arrange
        var input = new DecomposeProductionPlanInput
        {
            PlanId = 6 // 假设存在一个包含多个半成品的计划
        };

        // Act
        var result = await _productionPlanService.DecomposeProductionPlanAsync(input);

        // Assert
        Assert.True(result.Success);
        Assert.Contains("生产计划分解成功", result.Message);
        Assert.NotEmpty(result.DecomposedMaterials);
        
        // 验证生成了多个子BOM工单
        // 如果有2个半成品，应该生成2个SUB工单和1个MAIN工单
    }
}
