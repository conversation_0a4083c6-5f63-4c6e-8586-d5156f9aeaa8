// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Service.Bom.Dto.PageResult;
using Admin.NET.Application.Service.WorkOrders.DTO;
using Admin.NET.Core.Entity.MesEntity;
using AngleSharp.Dom;
using AutoMapper;
using Elastic.Clients.Elasticsearch.Nodes;
using Furion.DatabaseAccessor;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.WorkOrders;
[DynamicApiController]
public class WorkOrderService:IDynamicApiController
{
    private readonly SqlSugarRepository<ProductionPlan> planRep;
    private readonly SqlSugarRepository<Admin.NET.Core.Entity.MesEntity.WorkOrder> workRep;
    private readonly SqlSugarRepository<Product> productRep;
    private readonly SqlSugarRepository<BomEntity> bomRep;
    private readonly SqlSugarRepository<SourceType> sourRep;
    private readonly SqlSugarRepository<ProcessRoute> processrouteRep;
    private readonly SqlSugarRepository<BOMMaterials> bommaterialRep;
    private readonly SqlSugarRepository<MaterialEntity> materialRep;
    private readonly IMapper mapper;
    private readonly SqlSugarRepository<ProcessComposition> compositionRep;

    public WorkOrderService(SqlSugarRepository<ProductionPlan> planRep,SqlSugarRepository<Admin.NET.Core.Entity.MesEntity.WorkOrder> workRep,SqlSugarRepository<Product> productRep,SqlSugarRepository<BomEntity> bomRep,SqlSugarRepository<SourceType> sourRep,SqlSugarRepository<ProcessRoute> processrouteRep,SqlSugarRepository<BOMMaterials> bommaterialRep,SqlSugarRepository<MaterialEntity> materialRep,IMapper mapper,SqlSugarRepository<ProcessComposition> compositionRep)
    {
        this.planRep = planRep;
        this.workRep = workRep;
        this.productRep = productRep;
        this.bomRep = bomRep;
        this.sourRep = sourRep;
        this.processrouteRep = processrouteRep;
        this.bommaterialRep = bommaterialRep;
        this.materialRep = materialRep;
        this.mapper = mapper;
        this.compositionRep = compositionRep;
    }
    /// <summary>
    /// 获取生产工单列表
    /// </summary>
    /// <param name="search"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<GetPageResultDto>> GetWorkOrder([FromQuery] SearchDto search)
    {
        try
        {
            // 1. 查询工单主表信息
            var query = workRep.AsQueryable()
                .LeftJoin<ProductionPlan>((wo, plan) => wo.LinkPlanId == plan.Id)
                .LeftJoin<Product>((wo, plan, produ) => wo.LinkPlanId == plan.Id && plan.ProductId == produ.Id)
                .LeftJoin<BomEntity>((wo, plan, produ, bom) => wo.LinkPlanId == plan.Id && plan.ProductId == produ.Id && bom.Id == plan.BomId && bom.ProductId == plan.ProductId)
                .LeftJoin<ProcessRoute>((wo, plan, produ, bom, route) => wo.LinkPlanId == plan.Id && plan.ProductId == produ.Id && bom.Id == plan.BomId && bom.ProductId == plan.ProductId && route.Id == bom.ProcessRouteId)
                .WhereIF(search.Status.HasValue && search.Status.Value > 0, (wo, plan, produ) => wo.Status == search.Status.Value)
                .Where((wo, plan, produ) => wo.WorkOrderCode.Contains(search.WorkOrderCode))
                .Where((wo, plan, produ) => plan.PlanName.Contains(search.PlanName))
                .Where((wo, plan, produ) => produ.ProductName.Contains(search.ProductName))
                .Select((wo, plan, produ, bom, route) => new GetPageResultDto
                {
                    WorkOrderId = wo.Id,
                    WorkOrderCode = wo.WorkOrderCode,
                    WorkOrderName = wo.WorkOrderName,
                    ProductId = plan.ProductId.Value,
                    ProductCode = produ.ProductCode,
                    ProductName = produ.ProductName,
                    ProductType = produ.ProductType,
                    Specification = produ.Specification,
                    Unit = produ.Unit,
                    BomId = wo.BomId,
                    PlanName = plan.PlanName,
                    PlanNumber = plan.PlanNumber,
                    PlanEndTime = plan.PlanEndTime,
                    PlanStartTime = plan.PlanStartTime,
                    DemandTime = plan.DemandTime,
                    Status = wo.Status,
                    Remarks = bom.Remarks
                });

            var pagelist = await query.ToPagedListAsync(search.Index, search.Size);

            // 2. 获取工序组成数据并合并
            var workOrderCodes = pagelist.Items.Select(dto => dto.WorkOrderCode).ToList();

            var processStepsQuery = workRep.AsQueryable()
                .LeftJoin<ProductionPlan>((wo, plan) => wo.LinkPlanId == plan.Id)
                .LeftJoin<BomEntity>((wo, plan, bom) => plan.BomId == bom.Id)
                .LeftJoin<ProcessRoute>((wo, plan, bom, route) => bom.ProcessRouteId == route.Id)
                .LeftJoin<ProcessComposition>((wo, plan, bom, route, comp) => route.ProcessRouteCode == comp.ProcessRouteId)
                .Where((wo, plan, bom, route, comp) => workOrderCodes.Contains(wo.WorkOrderCode))
                .Select((wo, plan, bom, route, comp) => new {
                    WorkOrderCode = wo.WorkOrderCode,
                    ProcessStepId = comp.ProcessStepId
                });

            var processSteps = await processStepsQuery.ToListAsync();

            // 3. 按工单分组并合并工序步骤
            var stepGroups = processSteps
                .GroupBy(p => p.WorkOrderCode)
                .ToDictionary(g => g.Key, g => string.Join(",", g.Select(p => p.ProcessStepId).Distinct()));

            foreach (var item in pagelist.Items)
            {
                if (stepGroups.TryGetValue(item.WorkOrderCode, out var steps))
                {
                    item.ProcessSteps = steps; // 改为字符串类型存储多个工序
                }
            }

            return pagelist;
        }
        catch (Exception)
        {

            throw;
        }
    }

    /// <summary>
    ///（排产）
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    [UnitOfWork]
    public async Task<GetDetailDto> ProductSchedu([FromQuery]long WorkOrderId,[FromQuery]ProductionScheduingDto prodto)
    {
        try
        {
            var work = await workRep.GetByIdAsync(WorkOrderId);
            var plan = await planRep.GetByIdAsync(work.LinkPlanId);
            var product = await productRep.GetByIdAsync(plan.ProductId);
            var bom = await bomRep.GetByIdAsync(plan.BomId);
            var source = await sourRep.GetByIdAsync(plan.SourceId);
            var processroute = await processrouteRep.GetByIdAsync(bom.ProcessRouteId);
            var materials = await bommaterialRep.GetListAsync(x => x.BomID == bom.Id);
            var dto = new GetDetailDto
            {
                WorkOrderCode = work.WorkOrderCode,
                WorkOrderName = work.WorkOrderName,
                PlanName = plan.PlanName,
                PlanCode = plan.PlanCode,
                SourceId = plan.SourceId,
                SourceName = source.SourceName,
                ProductCode = product.ProductCode,
                ProductName = product.ProductName,
                Specification = product.Specification,
                Unit = product.Unit,
                ProductType = product.ProductType,
                BomId = bom.Id,
                BomVersion = bom.BomVersion,
                PlanStartTime = prodto.PlanStartTime,
                PlanEndTime = prodto.PlanEndTime,
                PlanNumber = prodto.PlanNumber,
                DemandTime = prodto.DemandTime,
                Remarks = prodto.Remarks,
                ProcessRouteCode = processroute.ProcessRouteCode,
                ProcessRouteName = processroute.ProcessRouteName,
                MaterialIds = materials.Select(x=>x.MaterialID).ToList(),
                PlanId = work.LinkPlanId,
            };
            var productl = await planRep.GetByIdAsync(dto.PlanId);
            productl.PlanStartTime = prodto.PlanStartTime;
            productl.PlanEndTime = prodto.PlanEndTime;
            productl.DemandTime = prodto.DemandTime;
            productl.PlanNumber = prodto.PlanNumber;
            work.Status=3;
            bom.Remarks = dto.Remarks;
            await planRep.AsUpdateable(productl).ExecuteCommandAsync();
            await workRep.AsUpdateable(work).ExecuteCommandAsync();
            await bomRep.AsUpdateable(bom).ExecuteCommandAsync();
            return dto;
        }
        catch (Exception)
        {

            throw;
        }
    }

    /// <summary>
    /// 获取基本信息
    /// </summary>
    /// <param name="WorkOrderId"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<GetDetailDto> GetDetail([FromQuery]long WorkOrderId)
    {
        try
        {
            var work = await workRep.GetByIdAsync(WorkOrderId);
            var plan = await planRep.GetByIdAsync(work.LinkPlanId);
            var product = await productRep.GetByIdAsync(plan.ProductId);
            var bom = await bomRep.GetByIdAsync(plan.BomId);
            var source = await sourRep.GetByIdAsync(plan.SourceId);
            var processroute = await processrouteRep.GetByIdAsync(bom.ProcessRouteId);
            var materials = await bommaterialRep.GetListAsync(x => x.BomID == bom.Id);
            var dto = new GetDetailDto
            {
                WorkOrderCode = work.WorkOrderCode,
                WorkOrderName = work.WorkOrderName,
                PlanName = plan.PlanName,
                PlanCode = plan.PlanCode,
                SourceId = plan.SourceId,
                SourceName = source.SourceName,
                ProductCode = product.ProductCode,
                ProductName = product.ProductName,
                Specification = product.Specification,
                Unit = product.Unit,
                ProductType = product.ProductType,
                BomId = work.BomId.Value,
                BomCode= bom.BomCode,
                BomVersion = bom.BomVersion,
                PlanStartTime = plan.PlanStartTime,
                PlanEndTime = plan.PlanEndTime,
                PlanNumber = plan.PlanNumber,
                DemandTime = plan.DemandTime,
                Remarks = bom.Remarks,
                ProcessRouteCode = processroute.ProcessRouteCode,
                ProcessRouteName = processroute.ProcessRouteName,
                MaterialIds = materials.Select(x => x.MaterialID).ToList(),
                PlanId = work.LinkPlanId,
                ProcessRouteId = processroute.Id,
                Status = work.Status,
            };
            // 2. 获取工序组成数据并合并
            var processSteps = await compositionRep.GetListAsync(u => u.ProcessRouteId == dto.ProcessRouteCode);

            // 3. 按工单分组并合并工序步骤
            var stepGroups = processSteps
                .GroupBy(p => p.ProcessRouteId)
                .ToDictionary(g => g.Key, g => string.Join(",", g.Select(p => p.ProcessStepId).Distinct()));

            //foreach (var item in pagelist.Items)
            //{
            //    if (stepGroups.TryGetValue(item.WorkOrderCode, out var steps))
            //    {
            //        item.ProcessSteps = steps; // 改为字符串类型存储多个工序
            //    }
            //}
            dto.ProcessSteps = string.Join(",", stepGroups.Values);
            return dto;
        }
        catch (Exception)
        {

            throw;
        }
    }

    /// <summary>
    /// 获取物料详情
    /// </summary>
    /// <param name="input"></param>
    /// <param name="bomId"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<SqlSugarPagedList<MaterialEntity>> GetMaterialEntityList([FromQuery]SimplePageInput input,[FromQuery]long bomId)
    {
        try
        {
            var materialids = (await bommaterialRep.GetListAsync(x => x.BomID == bomId)).Select(x => x.MaterialID).ToList();


            var list = new List<MaterialEntity>();
            foreach (var item in materialids)
            {
                list.Add(await materialRep.GetByIdAsync(item));
            }
            return list.ToPagedList(input.Page,input.PageSize);
        }
        catch (Exception)
        {

            throw;
        }
    }

    
}
