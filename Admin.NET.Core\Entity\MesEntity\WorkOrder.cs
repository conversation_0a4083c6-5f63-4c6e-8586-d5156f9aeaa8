﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Core.Entity.MesEntity;
/// <summary>
/// 生产工单
/// </summary>
[SugarTable(null, "生产工单表")]
[SysTable]
public class WorkOrder : EntityBase
{
    /// <summary>
    /// 工单编号
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public string? WorkOrderCode { get; set; }
    /// <summary>
    /// 工单名称
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public string? WorkOrderName { get; set; }
    /// <summary>
    /// 关联的计划
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public long? LinkPlanId { get; set; }
    /// <summary>
    /// BOMId
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public long? BomId { get; set; }
    /// <summary>
    /// 状态
    /// 0:待排产
    /// 1:未开始
    /// 2:进行中
    /// 3:已完成
    /// 4:已撤回
    /// 5:已取消
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int Status { get; set; }
}
