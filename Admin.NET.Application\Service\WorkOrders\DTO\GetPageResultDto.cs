﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Entity.MesEntity;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Application.Service.WorkOrders.DTO;
public class GetPageResultDto
{
    public long WorkOrderId { get; set; }
    /// <summary>
    /// 工单编号
    /// </summary>
    public string? WorkOrderCode { get; set; }
    /// <summary>
    /// 工单名称
    /// </summary>
    public string? WorkOrderName { get; set; }
    /// <summary>
    /// 工单进度
    /// </summary>
    public string ProcessSteps { get; set; }
    /// <summary>
    /// BOMId
    /// </summary>
    public long? BomId { get; set; }
    /// <summary>
    /// 关联计划
    /// </summary>
    public string? PlanName { get; set; }
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId { get; set; }
    /// <summary>
    /// 产品编号（唯一标识）312312213
    /// </summary>
    public string ProductCode { get; set; }
    /// <summary>
    /// 产品名称
    /// </summary>
    public string ProductName { get; set; }

    /// <summary>
    /// 产品规格型号
    /// </summary>
    public string Specification { get; set; }

    /// <summary>
    /// 产品计量单位（个/件/箱等）
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 产品类型（下拉选项值）
    /// </summary>
    public string ProductType { get; set; }

    /// <summary>
    /// 计划开工时间
    /// </summary>
    public DateTime? PlanStartTime { get; set; }
    /// <summary>
    /// 实际开工时间
    /// </summary>
    public DateTime? RealityStartTime { get; set; }
    /// <summary>
    /// 计划完工时间
    /// </summary>
    public DateTime? PlanEndTime { get; set; }
    /// <summary>
    /// 实际完工时间
    /// </summary>
    public DateTime? RealityEndTime { get; set; }
    /// <summary>
    /// 需求时间
    /// </summary>
    public DateTime? DemandTime { get; set; }
    /// <summary>
    /// 计划数量
    /// </summary>
    public int? PlanNumber { get; set; }
    /// <summary>
    /// 实际数量
    /// </summary>
    public int? RealityNumber { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remarks { get; set; }
    /// <summary>
    /// 状态
    /// 0:待排产
    /// 1:未开始
    /// 2:进行中
    /// 3:已完成
    /// 4:已撤回
    /// 5:已取消
    /// </summary>
    public int Status { get; set; }
}
