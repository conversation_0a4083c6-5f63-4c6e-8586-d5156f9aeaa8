// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin.NET.Core.Entity.MesEntity;
[SugarTable(null, "BOM表单")]
[SysTable]
public class BomEntity: EntityBaseDel
{
    /// <summary>
    /// BOM 编号
    /// </summary>
    public string BomCode { get; set; }

    /// <summary>
    /// 是否系统编号（开关状态）
    /// </summary>
    public bool IsSystemCode { get; set; }

    /// <summary>
    /// 是否默认 BOM
    /// </summary>
    public bool IsDefaultBom { get; set; }

    /// <summary>
    /// BOM 版本
    /// </summary>
    public string BomVersion { get; set; }

    /// <summary>
    /// 产品名称（必填，关联物料）
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// 产品编号
    /// </summary>
    public string ProductCode { get; set; }

    /// <summary>
    /// 规格型号
    /// </summary>
    public string Specification { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 日产量
    /// </summary>
    public int DailyOutput { get; set; }

    /// <summary>
    /// 备注信息
    /// </summary>
    public string Remarks { get; set; }

    /// <summary>
    /// 工艺路线Id
    /// </summary>
    [SugarColumn(ColumnDescription = "工艺路线Id", IsNullable = true)]
    public long ProcessRouteId { get; set; } //工艺路线Id
}
